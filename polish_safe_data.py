import pandas as pd

# Load the CSV file
# df = pd.read_csv("/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mttl_modularities_main/datasets/safe/beavertails/330k_test_beaverTails_safe_gpt4_qtype_4_drug_abuse_weapons_banned_substance.csv")  # Change to your actual file name
df = pd.read_csv("datasets/safe/beavertails/330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.csv")  # Change to your actual file name
unsafe_df = pd.read_csv("/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mttl_modularities_main/datasets/harm/beavertails/330k_train_beaverTails_unsafe_drug_abuse_weapons_banned_substance.csv")

# Define the unwanted phrase
unwanted_start = {"I'm sorry, but I can't assist with that.", "I'm sorry, I can't assist with that.", "SORRY, I CAN'T ASSIST WITH THAT.", "Sorry, I can't assist that.","I'm sorry, but I can't assist with that.", "I can't assist with that.", "I'm sorry, but I cannot assist with that.", "I'm sorry, but I can't assist with that.", "I'm sorry, but I cannot assist with that."}

# Remove the phrase from the beginning of responses
# def clean_response(response):
#     if isinstance(response, str):
#         for phrase in unwanted_start:
#             if response.strip().startswith(phrase):
#                 response = response[len(phrase)+1:].strip()
#                 break
#     return response


import unicodedata

def clean_response(response):
    # Replace Unicode apostrophes with standard ASCII apostrophes
    response = response.replace("’", "'")
    
    # Define refusal sentences to remove
    refusal_sentences = [
        "I'm sorry, I can't assist with that.",
        "I'm sorry, I cannot assist with that.",
        "I'm sorry, but I can't assist you with that.",
        "I'm sorry, but I cannot assist you with that.",
        "I'm sorry, but I can't assist with that.",
        "I'm sorry, but I cannot assist with that.",        
        "I'm sorry to hear that you're feeling this way, but I can't assist with that.",
        "I'm really sorry to hear that you're feeling this way",
        "I'm sorry, but I can't provide that information",
        "I'm sorry, but I cannot provide that information",
        "I'm sorry to hear that you're feeling this way, but",
        "Sorry, I can't assist with that.",
        "Sorry, I cannot assist with that.", 
        "I can't help with that.",
        "I can't assist with that.",
        "I can't assist you with that.",
        "I cannot help with that.",
        "I cannot assist with that.",
        "I cannot assist you with that.", 
        "I cannot provide assistance on this.", 
        "I cannot provide that information.",     
    ]
    
    # Remove refusal sentences
    for refusal in refusal_sentences:
        response = response.replace(refusal, "")
    
    # Check if there are more sentences left
    remaining_sentences = [sentence.strip() for sentence in response.split('.') if sentence.strip()]
    
    # Return cleaned response if there are more sentences, otherwise return an empty string
    return '.'.join(remaining_sentences) if remaining_sentences else ""




df['response'] = df['response'].apply(clean_response)

# Drop rows where the response is now empty or only whitespace
# df = df[df['response'].str.strip().astype(bool)]

# Identify non-empty responses
non_empty_mask = df['response'].str.strip().astype(bool)
df = df[non_empty_mask]

# If you want to also clean the original unsafe_df using the same mask:
unsafe_df = unsafe_df.loc[non_empty_mask[unsafe_df.index]]

# Save the cleaned DataFrame to a new CSV
df.to_csv("/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mttl_modularities_main/datasets/sampled/beavertails/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.csv", index=False)
unsafe_df.to_csv("/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mttl_modularities_main/datasets/sampled/beavertails/4_cleaned_330k_train_beaverTails_unsafe_drug_abuse_weapons_banned_substance.csv", index=False)
print("done!")