#!/usr/bin/env python3
"""
Script to verify the matching results by showing sample matches.
"""

import pandas as pd
import json
import numpy as np
from pathlib import Path

def verify_matches(csv_file, json_file, indices_file):
    """Verify matches by showing sample prompts."""
    print(f"Verifying matches for {csv_file}")
    
    # Load CSV
    csv_data = pd.read_csv(csv_file)
    
    # Load JSON
    with open(json_file, 'r', encoding='utf-8') as f:
        json_data = json.load(f)
    
    # Load indices
    indices = np.load(indices_file)
    
    print(f"CSV has {len(csv_data)} rows")
    print(f"JSON has {len(json_data)} rows")
    print(f"Found {len(indices)} matching indices")
    
    # Show first 5 matches
    print("\nFirst 5 matches:")
    csv_prompts = set(csv_data['prompt'].astype(str))
    
    count = 0
    for idx in indices[:5]:
        json_prompt = json_data[idx]['prompt']
        if str(json_prompt) in csv_prompts:
            print(f"Index {idx}: Match found")
            print(f"  Prompt: {json_prompt[:100]}...")
            count += 1
        else:
            print(f"Index {idx}: NO MATCH!")
            print(f"  JSON Prompt: {json_prompt[:100]}...")
    
    print(f"\nVerified {count}/5 sample matches")
    return count == 5

def main():
    """Main verification function."""
    # Test one file
    csv_file = "safe_pku/train_safe_gpt4_qtype2_environmental_damage.csv"
    json_file = "pku_saferlhf_processed/train_unsafe_environmental_damage.json"
    indices_file = "row_mappings/train_safe_gpt4_qtype2_environmental_damage_matching_indices.npy"
    
    if all(Path(f).exists() for f in [csv_file, json_file, indices_file]):
        verify_matches(csv_file, json_file, indices_file)
    else:
        print("Some files are missing!")
        for f in [csv_file, json_file, indices_file]:
            print(f"{f}: {'EXISTS' if Path(f).exists() else 'MISSING'}")

if __name__ == "__main__":
    main()
