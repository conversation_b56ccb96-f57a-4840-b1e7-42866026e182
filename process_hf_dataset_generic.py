#!/usr/bin/env python3
"""
Generic script to process any dataset from Hugging Face:
1. Load any dataset from Hugging Face
2. Select up to specified number of samples
3. Map any two columns to 'prompt' and 'response'
4. Split into train/test sets (90%/10% by default)
5. Save as separate JSON files in the specified folder

Usage examples:
- python process_hf_dataset_generic.py --dataset LLukas22/fiqa --prompt-col question --response-col answer
- python process_hf_dataset_generic.py --dataset microsoft/DialoGPT-medium --prompt-col input --response-col target
- python process_hf_dataset_generic.py --dataset squad --prompt-col question --response-col answers.text
"""

import json
import os
from datasets import load_dataset
import argparse
from typing import Tuple, Any
import time
import random


def split_train_test(data: list, test_ratio: float = 0.1, seed: int = 42) -> Tuple[list, list]:
    """
    Split data into train and test sets
    
    Args:
        data (list): List of data samples
        test_ratio (float): Ratio of data to use for testing (default: 0.1 for 10%)
        seed (int): Random seed for reproducibility
    
    Returns:
        Tuple[list, list]: (train_data, test_data)
    """
    # Set random seed for reproducibility
    random.seed(seed)
    
    # Shuffle the data
    shuffled_data = data.copy()
    random.shuffle(shuffled_data)
    
    # Calculate split index
    test_size = int(len(shuffled_data) * test_ratio)
    train_size = len(shuffled_data) - test_size
    
    # Split the data
    train_data = shuffled_data[:train_size]
    test_data = shuffled_data[train_size:]
    
    print(f"Data split: {len(train_data)} training samples, {len(test_data)} test samples")
    
    return train_data, test_data


def get_nested_value(item: dict, key_path: str) -> Any:
    """
    Get value from nested dictionary using dot notation
    
    Args:
        item (dict): Dictionary to extract value from
        key_path (str): Dot-separated path (e.g., 'answers.text')
    
    Returns:
        Any: The extracted value
    """
    keys = key_path.split('.')
    value = item
    
    for key in keys:
        if isinstance(value, dict) and key in value:
            value = value[key]
        elif isinstance(value, list) and key.isdigit():
            idx = int(key)
            if idx < len(value):
                value = value[idx]
            else:
                return None
        else:
            return None
    
    # If the final value is a list, take the first element
    if isinstance(value, list) and len(value) > 0:
        value = value[0]
    
    return value


def load_hf_dataset(dataset_name: str, max_samples: int = 10000, split: str = 'train', config_name: str = None):
    """
    Load any dataset from Hugging Face
    
    Args:
        dataset_name (str): Name of the dataset (e.g., 'LLukas22/fiqa')
        max_samples (int): Maximum number of samples to keep
        split (str): Dataset split to load ('train', 'test', etc.)
        config_name (str): Configuration name for datasets with multiple configs
    
    Returns:
        Dataset: The loaded dataset
    """
    print(f"Loading {dataset_name} dataset...")
    start_time = time.time()
    
    try:
        # Load dataset
        if config_name:
            dataset = load_dataset(dataset_name, config_name)
        else:
            dataset = load_dataset(dataset_name)
        
        # Get the specified split
        if split in dataset:
            data = dataset[split]
        else:
            available_splits = list(dataset.keys())
            print(f"Split '{split}' not found. Available splits: {available_splits}")
            data = dataset[available_splits[0]]
            print(f"Using split: {available_splits[0]}")
        
        # Limit to max_samples early to save memory
        if len(data) > max_samples:
            print(f"Dataset has {len(data)} samples, selecting first {max_samples}")
            data = data.shuffle(seed=42)  # Shuffle for randomness
            data = data.select(range(max_samples))
        else:
            print(f"Dataset has {len(data)} samples")
        
        load_time = time.time() - start_time
        print(f"Loading completed in {load_time:.2f} seconds")
        
        return data
        
    except Exception as e:
        print(f"Error loading dataset: {e}")
        print("Make sure the dataset name and split are correct")
        raise


def detect_columns(dataset, prompt_col: str = None, response_col: str = None):
    """
    Detect or validate column names for prompt and response
    
    Args:
        dataset: The loaded dataset
        prompt_col (str): Specified prompt column name
        response_col (str): Specified response column name
    
    Returns:
        Tuple[str, str]: (prompt_column, response_column)
    """
    # Get first sample to inspect structure
    sample = dataset[0]
    available_columns = list(sample.keys())
    
    print(f"Available columns: {available_columns}")
    
    # If columns are specified, validate them
    if prompt_col and response_col:
        # Check if columns exist (support nested keys)
        prompt_value = get_nested_value(sample, prompt_col)
        response_value = get_nested_value(sample, response_col)
        
        if prompt_value is None:
            raise ValueError(f"Prompt column '{prompt_col}' not found or empty")
        if response_value is None:
            raise ValueError(f"Response column '{response_col}' not found or empty")
        
        print(f"Using columns: '{prompt_col}' -> prompt, '{response_col}' -> response")
        return prompt_col, response_col
    
    # Auto-detect columns
    prompt_candidates = ['question', 'input', 'prompt', 'query', 'text', 'instruction']
    response_candidates = ['answer', 'output', 'response', 'target', 'completion', 'reply']
    
    detected_prompt = None
    detected_response = None
    
    for col in available_columns:
        col_lower = col.lower()
        if not detected_prompt and any(candidate in col_lower for candidate in prompt_candidates):
            detected_prompt = col
        elif not detected_response and any(candidate in col_lower for candidate in response_candidates):
            detected_response = col
    
    # Fallback to first two columns if auto-detection fails
    if not detected_prompt or not detected_response:
        if len(available_columns) >= 2:
            detected_prompt = available_columns[0]
            detected_response = available_columns[1]
            print(f"Auto-detection failed. Using first two columns: '{detected_prompt}', '{detected_response}'")
        else:
            raise ValueError("Could not detect prompt and response columns. Please specify them manually.")
    
    print(f"Auto-detected columns: '{detected_prompt}' -> prompt, '{detected_response}' -> response")
    return detected_prompt, detected_response


def process_hf_dataset(dataset, dataset_name: str, output_dir: str, prompt_col: str, response_col: str, 
                      enable_split: bool = True, test_ratio: float = 0.1):
    """
    Process any HF dataset and save as JSON with train/test split
    
    Args:
        dataset: The loaded dataset
        dataset_name (str): Name of the dataset for output files
        output_dir (str): Directory to save processed files
        prompt_col (str): Column name for prompts
        response_col (str): Column name for responses
        enable_split (bool): Whether to split data into train/test sets
        test_ratio (float): Ratio of data to use for testing (default: 0.1 for 10%)
    """
    print(f"Processing {dataset_name} dataset...")
    start_time = time.time()
    
    # Process data in batches for better memory efficiency
    batch_size = 1000
    processed_data = []
    skipped_count = 0
    
    # Process in batches
    total_samples = len(dataset)
    for i in range(0, total_samples, batch_size):
        end_idx = min(i + batch_size, total_samples)
        batch = dataset[i:end_idx]
        
        # Process batch
        for j in range(len(batch[list(batch.keys())[0]])):  # Use first key to get batch size
            # Extract values using the specified columns
            prompt_value = None
            response_value = None
            
            # Handle batch format
            if isinstance(batch, dict):
                # Create item dict for this index
                item = {key: batch[key][j] for key in batch.keys()}
                prompt_value = get_nested_value(item, prompt_col)
                response_value = get_nested_value(item, response_col)
            
            # Skip if values are None or empty
            if prompt_value is None or response_value is None:
                skipped_count += 1
                continue
            
            # Convert to string and clean
            prompt_str = str(prompt_value).strip()
            response_str = str(response_value).strip()
            
            if not prompt_str or not response_str:
                skipped_count += 1
                continue
            
            processed_data.append({
                'prompt': prompt_str,
                'response': response_str
            })
        
        # Show progress
        if i % (batch_size * 5) == 0:  # Every 5 batches
            print(f"  Processed {min(end_idx, total_samples)}/{total_samples} samples...")
    
    if skipped_count > 0:
        print(f"Skipped {skipped_count} samples due to missing/empty values")
    
    # Create safe filename from dataset name
    safe_name = dataset_name.replace('/', '_').replace('-', '_')
    
    # Split data into train/test if enabled
    if enable_split:
        train_data, test_data = split_train_test(processed_data, test_ratio=test_ratio)
        
        # Save train data
        train_filename = os.path.join(output_dir, f"{safe_name}_train.json")
        print(f"Saving training data to {train_filename}...")
        with open(train_filename, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, indent=2, ensure_ascii=False)
        
        # Save test data
        test_filename = os.path.join(output_dir, f"{safe_name}_test.json")
        print(f"Saving test data to {test_filename}...")
        with open(test_filename, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        process_time = time.time() - start_time
        print(f"Saved {len(train_data)} training samples to: {train_filename}")
        print(f"Saved {len(test_data)} test samples to: {test_filename}")
        print(f"Processing completed in {process_time:.2f} seconds")
    else:
        # Save as single JSON file (no split)
        filename = os.path.join(output_dir, f"{safe_name}.json")
        print(f"Saving to {filename}...")
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, indent=2, ensure_ascii=False)
        
        process_time = time.time() - start_time
        print(f"Saved {len(processed_data)} samples to: {filename}")
        print(f"Processing completed in {process_time:.2f} seconds")


def main():
    parser = argparse.ArgumentParser(description='Process any dataset from Hugging Face')
    parser.add_argument('--dataset', required=True,
                       help='Hugging Face dataset name (e.g., LLukas22/fiqa)')
    parser.add_argument('--output-dir', default='hf_datasets_processed', 
                       help='Output directory for processed files')
    parser.add_argument('--max-samples', type=int, default=10000,
                       help='Maximum number of samples to process')
    parser.add_argument('--split', default='train',
                       help='Dataset split to process')
    parser.add_argument('--config', default=None,
                       help='Dataset configuration name (if applicable)')
    parser.add_argument('--prompt-col', default=None,
                       help='Column name for prompts (auto-detect if not specified)')
    parser.add_argument('--response-col', default=None,
                       help='Column name for responses (auto-detect if not specified)')
    parser.add_argument('--enable-split', action='store_true', default=True,
                       help='Enable train-test split (default: True)')
    parser.add_argument('--no-split', action='store_true',
                       help='Disable train-test split (save as single file)')
    parser.add_argument('--test-ratio', type=float, default=0.1,
                       help='Ratio of data to use for testing (default: 0.1 for 10%%)')
    
    args = parser.parse_args()
    
    # Handle split arguments
    if args.no_split:
        args.enable_split = False
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"Processing dataset: {args.dataset}")
    print(f"Max samples: {args.max_samples}")
    print(f"Output directory: {args.output_dir}")
    print(f"Train-test split: {'Enabled' if args.enable_split else 'Disabled'}")
    if args.enable_split:
        print(f"Test ratio: {args.test_ratio:.1%}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # Load dataset
        dataset = load_hf_dataset(
            args.dataset, 
            max_samples=args.max_samples, 
            split=args.split,
            config_name=args.config
        )
        
        # Detect or validate columns
        prompt_col, response_col = detect_columns(dataset, args.prompt_col, args.response_col)
        
        # Process and save
        process_hf_dataset(
            dataset, 
            args.dataset, 
            args.output_dir, 
            prompt_col, 
            response_col,
            enable_split=args.enable_split, 
            test_ratio=args.test_ratio
        )
        
    except Exception as e:
        print(f"Error processing dataset: {e}")
        return
    
    total_time = time.time() - start_time
    print("-" * 50)
    print(f"Total processing time: {total_time:.2f} seconds")
    print(f"Processing complete! Files saved to: {args.output_dir}")
    
    # List generated files
    if os.path.exists(args.output_dir):
        print(f"\nGenerated files:")
        files = sorted(os.listdir(args.output_dir))
        for file in files:
            file_path = os.path.join(args.output_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  {file} ({size:,} bytes)")


if __name__ == "__main__":
    main()
