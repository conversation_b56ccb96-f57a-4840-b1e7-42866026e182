{"train_safe_gpt4_qtype2_cybercrime.csv": {"corresponding_json": "train_unsafe_cybercrime.json", "matching_rows": 32737, "output_file": "row_mappings/train_safe_gpt4_qtype2_cybercrime_matching_indices.npy"}, "train_safe_gpt4_qtype2_economic_crime.csv": {"corresponding_json": "train_unsafe_economic_crime.json", "matching_rows": 25324, "output_file": "row_mappings/train_safe_gpt4_qtype2_economic_crime_matching_indices.npy"}, "train_safe_gpt4_qtype2_environmental_damage.csv": {"corresponding_json": "train_unsafe_environmental_damage.json", "matching_rows": 2083, "output_file": "row_mappings/train_safe_gpt4_qtype2_environmental_damage_matching_indices.npy"}, "train_safe_gpt4_qtype3_cybercrime.csv": {"corresponding_json": "train_unsafe_cybercrime.json", "matching_rows": 32737, "output_file": "row_mappings/train_safe_gpt4_qtype3_cybercrime_matching_indices.npy"}, "train_safe_gpt4_qtype3_environmental_damage.csv": {"corresponding_json": "train_unsafe_environmental_damage.json", "matching_rows": 2083, "output_file": "row_mappings/train_safe_gpt4_qtype3_environmental_damage_matching_indices.npy"}, "train_safe_gpt4_qtype4_cybercrime.csv": {"corresponding_json": "train_unsafe_cybercrime.json", "matching_rows": 32737, "output_file": "row_mappings/train_safe_gpt4_qtype4_cybercrime_matching_indices.npy"}, "train_safe_gpt4_qtype4_environmental_damage.csv": {"corresponding_json": "train_unsafe_environmental_damage.json", "matching_rows": 2083, "output_file": "row_mappings/train_safe_gpt4_qtype4_environmental_damage_matching_indices.npy"}}