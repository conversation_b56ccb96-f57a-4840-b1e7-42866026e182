#!/usr/bin/env python3
"""
Script to match rows between safe_pku CSV files and pku_saferlhf_processed JSON files.
For each file in safe_pku, find which rows in the corresponding pku_saferlhf_processed file
match based on the prompt field, and store the row indices in numpy arrays.
"""

import pandas as pd
import json
import numpy as np
import os
from pathlib import Path
import re

def extract_category_from_filename(filename):
    """Extract category name from safe_pku filename."""
    # Pattern: train_safe_gpt4_qtype[2-4]_[category].csv
    match = re.search(r'train_safe_gpt4_qtype\d+_(.+)\.csv', filename)
    if match:
        return match.group(1)
    return None

def load_json_file(filepath):
    """Load JSON file and return the data."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return None

def load_csv_file(filepath):
    """Load CSV file and return the data."""
    try:
        return pd.read_csv(filepath)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return None

def find_matching_rows(safe_pku_file, pku_processed_file):
    """
    Find matching rows between safe_pku CSV and pku_saferlhf_processed JSON files.
    Returns numpy array of row indices from the JSON file that match prompts in CSV file.
    """
    print(f"Processing: {safe_pku_file} -> {pku_processed_file}")

    # Load CSV file - only read the prompt column
    try:
        csv_data = pd.read_csv(safe_pku_file, usecols=['prompt'])
    except Exception as e:
        print(f"Error loading CSV {safe_pku_file}: {e}")
        return None

    # Extract prompts from CSV and clean them
    csv_prompts = set()
    for prompt in csv_data['prompt']:
        if pd.notna(prompt):
            csv_prompts.add(str(prompt).strip())

    print(f"CSV file has {len(csv_prompts)} unique prompts")

    # Load and process JSON file in chunks to be memory efficient
    try:
        with open(pku_processed_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
    except Exception as e:
        print(f"Error loading JSON {pku_processed_file}: {e}")
        return None

    print(f"JSON file has {len(json_data)} entries")

    # Find matching row indices in JSON
    matching_indices = []
    for i, entry in enumerate(json_data):
        json_prompt = str(entry.get('prompt', '')).strip()
        if json_prompt in csv_prompts:
            matching_indices.append(i)

    print(f"Found {len(matching_indices)} matching rows")
    return np.array(matching_indices, dtype=np.int32)

def main():
    """Main function to process all files."""
    safe_pku_dir = Path('safe_pku')
    pku_processed_dir = Path('pku_saferlhf_processed')
    output_dir = Path('row_mappings')
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Get all CSV files in safe_pku directory
    csv_files = list(safe_pku_dir.glob('*.csv'))
    print(f"Found {len(csv_files)} CSV files in safe_pku directory")
    
    results = {}
    
    for csv_file in csv_files:
        # Extract category from filename
        category = extract_category_from_filename(csv_file.name)
        if not category:
            print(f"Could not extract category from {csv_file.name}")
            continue
        
        # Find corresponding JSON file
        json_file = pku_processed_dir / f'train_unsafe_{category}.json'
        
        if not json_file.exists():
            print(f"Corresponding JSON file not found: {json_file}")
            continue
        
        # Find matching rows
        # matching_indices = find_matching_rows(csv_file, json_file)
        
        # if matching_indices is not None:
        #     # Save numpy array
        #     output_file = output_dir / f'{csv_file.stem}_matching_indices.npy'
        #     np.save(output_file, matching_indices)
        #     print(f"Saved matching indices to {output_file}")
            
        #     # Store results for summary
        #     results[csv_file.name] = {
        #         'corresponding_json': json_file.name,
        #         'matching_rows': len(matching_indices),
        #         'output_file': str(output_file)
        #     }
        
        print("-" * 50)
        output_file = output_dir / f'{csv_file.stem}_matching_indices.npy'
        x = np.load(output_file)
    
    # Save summary
    summary_file = output_dir / 'matching_summary.json'
    with open(summary_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nSummary saved to {summary_file}")
    print("\nResults:")
    for csv_name, info in results.items():
        print(f"{csv_name} -> {info['corresponding_json']}: {info['matching_rows']} matches")

if __name__ == "__main__":
    main()
